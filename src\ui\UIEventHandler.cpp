#include "UIEventHandler.h"
#include "../sequencer/Sequencer.h"
#include "../midi/MidiManager.h"
#include "ButtonManager.h"

// External function declarations that the UI calls
extern void onClockStart();
extern void onClockStop();
extern void setLEDTheme(LEDTheme theme);

// External variables that are still needed from the main file
extern uint8_t currentScale;
extern bool isClockRunning;
extern const ParameterDefinition CORE_PARAMETERS[];

// Helper function declarations (static to this file)
static void handleParameterTouchEvent(uint8_t buttonIndex, bool pressed, UIState &uiState);
static bool handleParameterButtonEvent(const MatrixButtonEvent &evt, UIState &uiState);
static bool handleStepButtonEvent(const MatrixButtonEvent &evt, UIState &uiState, Sequencer &seq1, Sequencer &seq2);
static void handleLFOAssignment(uint8_t buttonIndex, UIState &uiState, Sequencer &seq1, Sequencer &seq2);
static void autoSelectAS5600Parameter(ParamId paramId, UIState &uiState);
static void handleAS5600ParameterControl(UIState &uiState);
static void handleControlButtonEvent(uint8_t buttonIndex, UIState &uiState, Sequencer &seq1, Sequencer &seq2);

void initUIEventHandler(UIState &uiState)
{
    initButtonManager(uiState);
}

void touchEventHandler(uint8_t buttonIndex, bool pressed, UIState &uiState, Sequencer &seq1, Sequencer &seq2)
{
    if (buttonIndex >= 0 && buttonIndex <= 8) { 
        handleParameterTouchEvent(buttonIndex, pressed, uiState);
    } else { 
        if (pressed)
        {
            Serial.print("Touch button pressed: ");
            Serial.println(buttonIndex);

            switch (buttonIndex)
            {
            case 9:
                if (!seq1.isNotePlaying() && !seq2.isNotePlaying())
                { 
                    Serial.println("[Touch] Live Record Mode launched (sequencer stopped)");
                }
                else
                {
                    Serial.println("[Touch] Live Record Mode ignored (sequencer running)");
                }
                break;
            case 10:
                uiState.isVoice2Mode = !uiState.isVoice2Mode;
                Serial.print("[Touch] Voice2 Mode toggled: ");
                Serial.println(uiState.isVoice2Mode ? "ON" : "OFF");
                break;
            case 11:
                uiState.lfoAssignMode = true;
                Serial.println("[Touch] LFO Assignment Mode enabled");
                break;
            default:
                break;
            }
        }
        else
        {
            Serial.print("Touch button released: ");
            Serial.println(buttonIndex);

            switch (buttonIndex)
            {
            case 11:
                uiState.lfoAssignMode = false;
                Serial.println("[Touch] LFO Assignment Mode exited (release)");
                break;
            default:
                break;
            }
        }
    }
}
// This are the non matrix buttons, mostly used to select the AS5600 parameter
static void handleParameterTouchEvent(uint8_t buttonIndex, bool pressed, UIState &uiState)
{
    if (pressed)
    {
        Serial.print("Parameter button pressed: ");
        Serial.println(buttonIndex);

        switch (buttonIndex)
        {
        case 0:
            uiState.currentAS5600Parameter = AS5600ParameterMode::Tempo;
            Serial.println("[Touch] AS5600 assigned to TEMPO");
            break;
        case 1:
            uiState.shuffleSelectMode = true;
            uiState.currentAS5600Parameter = AS5600ParameterMode::Shuffle;
            Serial.println("[Touch] Shuffle Template Mode enabled");
            break;
        case 2:
            if (isClockRunning) {
            onClockStop();
            Serial.println("[Touch] Sequencer STOP triggered");
            } else {
            onClockStart();
            Serial.println("[Touch] Sequencer START triggered");
            }
            break;
        case 3:
            uiState.currentAS5600Parameter = AS5600ParameterMode::DelayFeedback;
            Serial.println("[Touch] AS5600 assigned to Delay Feedback");
            break;
        case 4:
            uiState.currentAS5600Parameter = AS5600ParameterMode::DelayTime;
            Serial.println("[Touch] AS5600 assigned to Delay Time");
            break;
        case 5:
            uiState.currentAS5600Parameter = AS5600ParameterMode::LFO1freq;
            Serial.println("[Touch] AS5600 assigned to LFO1 Frequency");
            break;
        case 6:
            uiState.currentAS5600Parameter = AS5600ParameterMode::LFO1amp;
            Serial.println("[Touch] AS5600 assigned to LFO1 Amplitude");
            break;
        case 7:
            uiState.currentAS5600Parameter = AS5600ParameterMode::LFO2freq;
            Serial.println("[Touch] AS5600 assigned to LFO2 Frequency");
            break;
        case 8:
          
        }
    } else {
        Serial.print("Parameter button released: ");
        Serial.println(buttonIndex);

        switch (buttonIndex)
        {
        case 1:
            uiState.shuffleSelectMode = false;
            Serial.println("[Touch] Shuffle Template Mode exited (release)");
            break;
        }
    }
}

void matrixEventHandler(const MatrixButtonEvent &evt, UIState &uiState, Sequencer &seq1, Sequencer &seq2, MidiNoteManager &midiNoteManager)
{
    if (uiState.lfoAssignMode)
    {
        if (evt.type == MATRIX_BUTTON_PRESSED)
        {
            handleLFOAssignment(evt.buttonIndex, uiState, seq1, seq2);
            uiState.lfoAssignMode = false;
            Serial.println("Exited LFO assignment mode");
        }
        return;
    }



    if (handleParameterButtonEvent(evt, uiState))
        return;
    if (handleStepButtonEvent(evt, uiState, seq1, seq2))
        return;



    if (evt.type == MATRIX_BUTTON_PRESSED)
    {
        handleControlButtonEvent(evt.buttonIndex, uiState, seq1, seq2);
    }
}

static bool handleParameterButtonEvent(const MatrixButtonEvent &evt, UIState &uiState)
{
    for (size_t i = 0; i < PARAM_BUTTON_MAPPINGS_SIZE; ++i)
    {
        const auto &mapping = PARAM_BUTTON_MAPPINGS[i];
        if (evt.buttonIndex == mapping.buttonIndex)
        {
            bool pressed = (evt.type == MATRIX_BUTTON_PRESSED);

            if (mapping.paramId == ParamId::Slide)
            {
                if (pressed)
                {
                    uiState.slideMode = !uiState.slideMode;
                    Serial.print("Slide mode ");
                    Serial.println(uiState.slideMode ? "ON" : "OFF");
                }
                uiState.parameterButtonHeld[static_cast<int>(mapping.paramId)] = pressed;
                return true;
            }

            uiState.parameterButtonHeld[static_cast<int>(mapping.paramId)] = pressed;

            Serial.print("Button ");
            Serial.print(mapping.buttonIndex);
            Serial.print(" (");
            Serial.print(mapping.name);
            Serial.print(") ");
            Serial.println(pressed ? "pressed" : "released");

            if (pressed && mapping.paramId != ParamId::Note)
            {
                autoSelectAS5600Parameter(mapping.paramId, uiState);
            }
            return true;
        }
    }
    return false;
}

static bool handleStepButtonEvent(const MatrixButtonEvent &evt, UIState &uiState, Sequencer &seq1, Sequencer &seq2)
{
    if (evt.buttonIndex >= NUMBER_OF_STEP_BUTTONS)
    {
        return false;
    }

    Sequencer &currentActiveSeq = uiState.isVoice2Mode ? seq2 : seq1;

    if (uiState.slideMode && evt.type == MATRIX_BUTTON_PRESSED)
    {
        uint8_t currentSlideValue = currentActiveSeq.getStepParameterValue(ParamId::Slide, evt.buttonIndex);
        uint8_t newSlideValue = (currentSlideValue > 0) ? 0 : 1;
        currentActiveSeq.setStepParameterValue(ParamId::Slide, evt.buttonIndex, newSlideValue);
        Serial.print("Step ");
        Serial.print(evt.buttonIndex);
        Serial.print(" slide ");
        Serial.println(newSlideValue > 0 ? "ON" : "OFF");
        return true;
    }

    if (isAnyParameterButtonHeld(uiState) && evt.type == MATRIX_BUTTON_PRESSED)
    {
        const ParamButtonMapping *heldMapping = getHeldParameterButton(uiState);
        if (heldMapping)
        {
            uint8_t newStepCount = evt.buttonIndex + 1;
            currentActiveSeq.setParameterStepCount(heldMapping->paramId, newStepCount);
            Serial.print("Set ");
            Serial.print(heldMapping->name);
            Serial.print(" parameter length to ");
            Serial.println(newStepCount);
        }
        return true;
    }

    if (!isAnyParameterButtonHeld(uiState))
    {
        if (evt.type == MATRIX_BUTTON_PRESSED)
        {
            uiState.padPressTimestamps[evt.buttonIndex] = millis();
        }
        else if (evt.type == MATRIX_BUTTON_RELEASED)
        {
            unsigned long pressDuration = millis() - uiState.padPressTimestamps[evt.buttonIndex];
            uiState.padPressTimestamps[evt.buttonIndex] = 0;

            if (isLongPress(pressDuration))
            {
                uiState.selectedStepForEdit = (uiState.selectedStepForEdit == evt.buttonIndex) ? -1 : evt.buttonIndex;
            }
            else
            {
                currentActiveSeq.toggleStep(evt.buttonIndex);
                uiState.selectedStepForEdit = -1;
            }
        }
    }
    return true;
}

static void handleControlButtonEvent(uint8_t buttonIndex, UIState &uiState, Sequencer &seq1, Sequencer &seq2)
{
    switch (buttonIndex)
    {
    case BUTTON_AS5600_CONTROL:
        handleAS5600ParameterControl(uiState);
        break;
    case BUTTON_PLAY_STOP:
        if (isClockRunning)
            onClockStop();
        else
            onClockStart();
        uiState.flash25Until = millis() + CONTROL_LED_FLASH_DURATION_MS;
        break;
    case BUTTON_CHANGE_SCALE:
        currentScale = (currentScale + 1) % 7;
        break;
    case BUTTON_CHANGE_THEME:
        uiState.currentThemeIndex = (uiState.currentThemeIndex + 1) % static_cast<int>(LEDTheme::COUNT);
        setLEDTheme(static_cast<LEDTheme>(uiState.currentThemeIndex));
        break;
    case BUTTON_RESET_SEQUENCERS:
        seq1.resetAllSteps();
        seq2.resetAllSteps();
        uiState.resetStepsLightsFlag = true;
        break;
    case BUTTON_RANDOMIZE_SEQ1:
        seq1.randomizeParameters();
        uiState.selectedStepForEdit = -1;
        uiState.flash31Until = millis() + CONTROL_LED_FLASH_DURATION_MS;
        break;
    case BUTTON_RANDOMIZE_SEQ2:
        seq2.randomizeParameters();
        uiState.selectedStepForEdit = -1;
        uiState.flash31Until = millis() + CONTROL_LED_FLASH_DURATION_MS;
        break;
    case BUTTON_TOGGLE_DELAY:
        uiState.delayOn = !uiState.delayOn;
        uiState.flash23Until = millis() + CONTROL_LED_FLASH_DURATION_MS;
        if (uiState.delayOn)
        {
            uiState.currentAS5600Parameter = AS5600ParameterMode::DelayTime;
            Serial.println("Delay ON - AS5600 set to Delay Time");
        }
        else
        {
            Serial.println("Delay OFF");
        }
        break;
    }
}

static void handleLFOAssignment(uint8_t buttonIndex, UIState &uiState, Sequencer &seq1, Sequencer &seq2)
{
    Sequencer &currentActiveSeq = uiState.isVoice2Mode ? seq2 : seq1;
    ParamId paramId = ParamId::Count;

    for (size_t i = 0; i < PARAM_BUTTON_MAPPINGS_SIZE; ++i)
    {
        if (PARAM_BUTTON_MAPPINGS[i].buttonIndex == buttonIndex)
        {
            paramId = PARAM_BUTTON_MAPPINGS[i].paramId;
            break;
        }
    }

    if (paramId != ParamId::Count)
    {
        uint8_t lfoNum = uiState.isVoice2Mode ? 2 : 1;
        currentActiveSeq.assignLFO(lfoNum, paramId);
        Serial.print("Assigned LFO");
        Serial.print(lfoNum);
        Serial.print(" to ");
        Serial.println(CORE_PARAMETERS[static_cast<int>(paramId)].name);
    }
}

static void autoSelectAS5600Parameter(ParamId paramId, UIState &uiState)
{
    AS5600ParameterMode newAS5600Param;
    bool isValid = true;
    switch (paramId)
    {
    case ParamId::Vel:
        newAS5600Param = AS5600ParameterMode::Vel;
        break;
    case ParamId::Filter:
        newAS5600Param = AS5600ParameterMode::Filter;
        break;
    case ParamId::Attack:
        newAS5600Param = AS5600ParameterMode::Attack;
        break;
    case ParamId::Decay:
        newAS5600Param = AS5600ParameterMode::Decay;
        break;
    default:
        isValid = false;
        break;
    }

    if (isValid && newAS5600Param != uiState.currentAS5600Parameter)
    {
        uiState.currentAS5600Parameter = newAS5600Param;
        Serial.print("AS5600 auto-selected: ");
        Serial.println(CORE_PARAMETERS[static_cast<int>(paramId)].name);
    }
}

static void handleAS5600ParameterControl(UIState &uiState)
{
    uiState.currentAS5600Parameter = static_cast<AS5600ParameterMode>(
        (static_cast<uint8_t>(uiState.currentAS5600Parameter) + 1) %
        static_cast<uint8_t>(AS5600ParameterMode::COUNT));

    uiState.lastAS5600ButtonPress = millis();

    Serial.print("AS5600 parameter switched to: ");
    switch (uiState.currentAS5600Parameter)
    {
    case AS5600ParameterMode::Vel:
        Serial.println("Vel");
        break;
    case AS5600ParameterMode::Filter:
        Serial.println("Filter");
        break;
    case AS5600ParameterMode::Attack:
        Serial.println("Attack");
        break;
    case AS5600ParameterMode::Decay:
        Serial.println("Decay");
        break;
    case AS5600ParameterMode::DelayTime:
        Serial.println("Delay Time");
        break;
    case AS5600ParameterMode::DelayFeedback:
        Serial.println("Delay Feedback");
        break;
    case AS5600ParameterMode::LFO1freq:
        Serial.println("LFO1 Frequency");
        break;
    case AS5600ParameterMode::LFO1amp:
        Serial.println("LFO1 Amplitude");
        break;
    case AS5600ParameterMode::LFO2freq:
        Serial.println("LFO2 Frequency");
        break;
    case AS5600ParameterMode::LFO2amp:
        Serial.println("LFO2 Amplitude");
        break;
    case AS5600ParameterMode::COUNT:
        break; // Should not happen
    }
}