#ifndef INCLUDES_H
#define INCLUDES_H

#include "/audio/audio.h"
#include "/audio/audio_i2s.h"
#include "/dsp/adsr.h"
#include "/dsp/ladder.h"
#include "/dsp/svf.h"
#include "/dsp/oscillator.h"
#include "/dsp/delayline.h"
#include "/matrix/Matrix.h"
#include "/sequencer/Sequencer.h"
#include "/sequencer/SequencerDefs.h"
#include "/LEDMatrix/ledMatrix.h"
#include "/LEDMatrix/LEDMatrixFeedback.h"
#include "/LEDMatrix/LEDController.h"
#include "/sensors/DistanceSensor.h"
#include "/sensors/as5600.h"
#include "/sensors/AS5600Manager.h"
#include "/midi/MidiManager.h"
#include "/ui/UIEventHandler.h"
#include "/ui/touchButtonHandler.h"
#include "/ui/ButtonManager.h"
#include "/ui/UIState.h"
#include "/sequencer/ShuffleTemplates.h"

#endif // INCLUDES_H