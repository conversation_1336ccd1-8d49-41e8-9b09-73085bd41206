// Touch button state arrays: 0-11 = touchButtons, 12-23 = touchMatrix
constexpr uint8_t kBankSize = 12;
constexpr uint8_t kNumButtons = kBankSize * 1;
bool touchButtonState[kNumButtons] = {false};
bool prevTouchButtonState[kNumButtons] = {false};

// If you have flags indicating sensor presence, set them accordingly
extern bool touchButtonsPresent;
extern bool touchMatrixPresent;

void handleTouchButtons() {
  // Read current touched state from both sensors
  uint16_t touchedValueButtons = 0;
  uint16_t touchedValueMatrix = 0;

  // Defensive: zero out state arrays if sensors are absent to avoid spurious edges
  if (!touchButtonsPresent) {
    memset(&touchButtonState[0], 0, kBankSize);
  }
  if (!touchMatrixPresent) {
    memset(&touchButtonState[kBankSize], 0, kBankSize);
  }

  if (touchButtonsPresent) {
    touchedValueButtons = touchButtons.touched();
  }
  if (touchMatrixPresent) {
    touchedValueMatrix = touchMatrix.touched();
  }

  // Handle electrodes 0-11 from touchButtons (indices 0-11)
  for (uint8_t i = 0; i < 12; i++) {
    bool currState = false;
    if (touchButtonsPresent) {
      currState = (touchedValueButtons & (1 << i));
    }
    touchButtonState[i] = currState;

    // Edge detection
    if (touchButtonState[i] && !prevTouchButtonState[i]) {
        touchEventHandler(i, true, uiState, seq1, seq2);
    } else if (!touchButtonState[i] && prevTouchButtonState[i]) {
        touchEventHandler(i, false, uiState, seq1, seq2);
    }
    prevTouchButtonState[i] = touchButtonState[i];
  }

  // Handle electrodes 0-11 from touchMatrix (indices 12-23)
  for (uint8_t i = 0; i < 12; i++) {
    bool currState = false;
    if (touchMatrixPresent) {
      currState = (touchedValueMatrix & (1 << i));
    }
    uint8_t idx = i + 12;
    touchButtonState[idx] = currState;

    // Edge detection
    if (touchButtonState[idx] && !prevTouchButtonState[idx]) {
      onTouchButtonPressed(idx);
    } else if (!touchButtonState[idx] && prevTouchButtonState[idx]) {
      onTouchButtonReleased(idx);
    }
    prevTouchButtonState[idx] = touchButtonState[idx];
  }
}