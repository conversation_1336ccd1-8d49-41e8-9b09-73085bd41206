# MIDI Manager

**Summary:**
Handles all MIDI communications for the PicoMudrasSequencer, including note events, monophonic voice management, and centralized MIDI logic.

---

## Files

- `MidiManager.h`: Declares functions for sending MIDI note on/off messages for both voices and an `allNotesOff` function.
- `MidiManager.cpp`: Implements the MIDI functions, extracted from the main `.ino` file for improved modularity.

---

## Responsibilities

- Sending MIDI note events (on/off).
- Handling monophonic behavior for each voice.
- Providing a central place for all MIDI-related logic, including CC parameter mapping.

---

## See Also

- [Main Project README](../../README.md)
- [Sequencer Module](../sequencer/sequencerREADME.md)
