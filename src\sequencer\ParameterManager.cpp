#include "ParameterManager.h"
#include <algorithm>                  // For std::max, std::min
#include <cmath>                      // For roundf
#include <random>                     // For std::default_random_engine, std::uniform_real_distribution
#include <chrono>                     // For std::chrono::system_clock (for seeding)
#include <variant>                    // For std::visit
#include "../sensors/as5600.h"        // For AS5600ParameterMode
#include "../sensors/AS5600Manager.h" // For MAX_DELAY_SAMPLES extern declaration
#include <uClock.h>                   // For uClock global object

// AS5600 parameter bounds management functions moved to src/sensors/AS5600Manager.cpp

// Helper function to safely get float from ParameterValueType variant
// This function is internal to ParameterManager.cpp
static float getFloatFromParameterValueType(const ParameterValueType &v)
{
    return std::visit([](auto &&arg) -> float
                      {
                          using T = std::decay_t<decltype(arg)>;
                          if constexpr (std::is_same_v<T, float>)
                              return arg;
                          if constexpr (std::is_same_v<T, int>)
                              return static_cast<float>(arg);
                          if constexpr (std::is_same_v<T, bool>)
                              return arg ? 1.0f : 0.0f;
                          return 0.0f; // Fallback for unexpected types
                      },
                      v);
}

ParameterManager::ParameterManager()
{
    // Initialize the spin lock in the constructor
    _lock = spin_lock_init(spin_lock_claim_unused(true)); // Claim a unique lock number
}

void ParameterManager::init()
{
    for (size_t i = 0; i < static_cast<size_t>(ParamId::Count); ++i)
    {
        // Initialize each track with its default value from CORE_PARAMETERS
        _tracks[i].init(getFloatFromParameterValueType(CORE_PARAMETERS[i].defaultValue));
    }
}

void ParameterManager::setStepCount(ParamId id, uint8_t steps)
{
    _tracks[static_cast<size_t>(id)].resize(steps);
}

uint8_t ParameterManager::getStepCount(ParamId id) const
{
    uint8_t count = _tracks[static_cast<size_t>(id)].stepCount;
    return count;
}

float ParameterManager::getValue(ParamId id, uint8_t stepIdx) const
{
    float value = _tracks[static_cast<size_t>(id)].getValue(stepIdx);
    return value;
}

void ParameterManager::setValue(ParamId id, uint8_t stepIdx, float value)
{

    // Apply clamping and rounding based on parameter definition
    const auto &paramDef = CORE_PARAMETERS[static_cast<size_t>(id)];
    float minVal = getFloatFromParameterValueType(paramDef.minValue);
    float maxVal = getFloatFromParameterValueType(paramDef.maxValue);

    float clampedValue = std::max(minVal, std::min(value, maxVal));

    if (paramDef.isBinary)
    { // For boolean parameters, round to 0 or 1
        clampedValue = (clampedValue > 0.5f) ? 1.0f : 0.0f;
    }
    else if (paramDef.minValue.index() == 0)
    { // If min value is int, assume integer parameter
        clampedValue = roundf(clampedValue);
    }

    // DEBUG: Trace parameter setting (only for Note parameter to reduce spam)
    /*
    if (id == ParamId::Note) {
        Serial.print("[PARAM SET DEBUG] Note step ");
        Serial.print(stepIdx);
        Serial.print(": ");
        Serial.print(value, 2);
        Serial.print(" -> ");
        Serial.print(clampedValue, 2);
        Serial.print(" (range: ");
        Serial.print(minVal, 2);
        Serial.print("-");
        Serial.print(maxVal, 2);
        Serial.println(")");
    }
    */

    _tracks[static_cast<size_t>(id)].setValue(stepIdx, clampedValue);

    // --- Runtime Swing-to-Shuffle Mapping ---
    // When the Swing parameter is updated, dynamically compute and apply a shuffle template.
    // - If value is 0.0, shuffle is disabled.
    // - If value > 0.0, compute tick offset and enable shuffle with template.
    // This logic is minimal and only in the parameter update handler.
    if (id == ParamId::Swing) {
        // Diagnostic output for swing parameter changes
        Serial.print("[SWING] Runtime update: value=");
        Serial.println(clampedValue, 3);

        if (clampedValue <= 0.0f) {
            uClock.setShuffle(false);
            Serial.println("[SWING] Shuffle disabled (swing=0.0)");
        } else {
            // For PPQN=120 per step, max offset = 60 ticks (50% swing)
            const int PPQN_PER_STEP = 120;
            int maxOffset = PPQN_PER_STEP / 2;
            int offset = static_cast<int>(clampedValue * maxOffset);

            // Clamp offset to [1, maxOffset] to avoid zero-offset shuffle
            if (offset < 1) offset = 1;
            if (offset > maxOffset) offset = maxOffset;

            uint8_t shuffleStepTicks[2] = {0, static_cast<uint8_t>(offset)};
            uClock.setShuffleTemplate(shuffleStepTicks, 2);
            uClock.setShuffle(true);

            Serial.print("[SWING] Shuffle enabled: offset=");
            Serial.print(offset);
            Serial.print(" (PPQN=");
            Serial.print(PPQN_PER_STEP);
            Serial.println(")");
        }
    }
}

void ParameterManager::randomizeParameters()
{
    // Use a better random number generator
    static std::default_random_engine generator(std::chrono::system_clock::now().time_since_epoch().count());

    for (size_t i = 0; i < static_cast<size_t>(ParamId::Count); ++i)
    {
        ParamId currentParamId = static_cast<ParamId>(i);

        // When randomizing, ensure the Slide parameter's length is set to max
        if (currentParamId == ParamId::Slide)
        {
            _tracks[i].stepCount = 16;
        }

        const auto &paramDef = CORE_PARAMETERS[i];
        float minVal = getFloatFromParameterValueType(paramDef.minValue);
        float maxVal = getFloatFromParameterValueType(paramDef.maxValue);
        std::uniform_real_distribution<float> distribution(minVal, maxVal);

        for (uint8_t step = 0; step < _tracks[i].stepCount; ++step)
        {
            // For slide and gate, we want a 10% chance of being 1, otherwise 0
            if (currentParamId == ParamId::Slide)
            {

                std::uniform_int_distribution<int> slide_dist(0, 12);
                int slide_value = (slide_dist(generator) == 0) ? 1 : 0;
                _tracks[i].setValue(step, slide_value);
            }
            else if (currentParamId == ParamId::Gate)
            {
                if ((step % 2) == 0) // If the step is even
                {
                    // 75% chance of being 1, otherwise 0
                    std::uniform_int_distribution<int> gate_dist(0, 3);   // 0, 1, 2 for 1; 3 for 0
                    int gate_value = (gate_dist(generator) == 0) ? 0 : 1; // Corrected logic for 75%
                    _tracks[i].setValue(step, gate_value);
                }

                else
                {
                     std::uniform_int_distribution<int> gate_dist(0, 2);
                     int gate_value = (gate_dist(generator) == 0) ? 1 : 0;
                     _tracks[i].setValue(step, gate_value);
                }
            }
            else if (currentParamId == ParamId::GateSize)
            {
                std::uniform_real_distribution<float> gate_size_dist(0.1f, 0.3f);
                float gateSizeValue = gate_size_dist(generator);
                _tracks[i].setValue(step, gateSizeValue);
            }
            else if (currentParamId == ParamId::Filter)
            {
                std::uniform_real_distribution<float> gate_size_dist(0.2f, 0.7f);
                float gateSizeValue = gate_size_dist(generator);
                _tracks[i].setValue(step, gateSizeValue);
            }

            else if (currentParamId == ParamId::Attack)
            {
                std::uniform_real_distribution<float> gate_size_dist(0.0f, 0.15f);
                float gateSizeValue = gate_size_dist(generator);
                _tracks[i].setValue(step, gateSizeValue);
            }

            else if (currentParamId == ParamId::Decay)
            {
                std::uniform_real_distribution<float> gate_size_dist(0.01f, 0.75f);
                float gateSizeValue = gate_size_dist(generator);
                _tracks[i].setValue(step, gateSizeValue);
            }
            else
            {
                float randomValue = distribution(generator);
                _tracks[i].setValue(step, randomValue);
            }
        }
    }
}
