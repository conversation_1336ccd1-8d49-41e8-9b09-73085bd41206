#ifndef TOUCHBUTTONHANDLER_H
#define TOUCHBUTTONHANDLER_H

#include <cstdint>

// Function to be called in the main loop to handle touch button events.
void handleTouchButtons();

// These external variables should be defined in the main .ino file or another central location.
// They indicate whether the touch sensor hardware is present and initialized.
extern bool touchButtonsPresent;
extern bool touchMatrixPresent;

// Constants defining the layout of the touch buttons.
constexpr uint8_t kBankSize = 12;
constexpr uint8_t kNumButtons = kBankSize * 2;

// These functions need to be implemented by the UI event handler to process the button events.
void onTouchButtonPressed(uint8_t buttonIndex);
void onTouchButtonReleased(uint8_t buttonIndex);

#endif // TOUCHBUTTONHANDLER_H
